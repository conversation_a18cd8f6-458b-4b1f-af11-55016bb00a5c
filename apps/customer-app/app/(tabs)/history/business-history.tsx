import React, { useState, useEffect, useCallback } from 'react';
import { ScrollView, ActivityIndicator } from 'react-native';
import { Box } from '@indie-points/ui-box';
import { VStack } from '@indie-points/ui-vstack';
import { HStack } from '@indie-points/ui-hstack';
import { Heading } from '@indie-points/ui-heading';
import { Text } from '@indie-points/ui-text';
import { Pressable } from '@indie-points/ui-pressable';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { useAuth } from '@indie-points/contexts';
import {
  TransactionsService,
  CustomerTransaction,
  CustomerRewardEligibility,
} from '../../../services';
import dayjs from 'dayjs';
import { GradientBar } from '@indie-points/auth';
import { RefreshControl } from '@indie-points/ui-refresh-control';
import { useLocalSearchParams, useRouter } from 'expo-router';
import * as Haptics from 'expo-haptics';

export default function BusinessHistory() {
  const { user } = useAuth();
  const router = useRouter();
  const { businessId, businessName, businessCategory } = useLocalSearchParams<{
    businessId: string;
    businessName: string;
    businessCategory: string;
  }>();

  // Tab state
  const [activeTab, setActiveTab] = useState<'transactions' | 'rewards'>(
    'transactions'
  );

  // Transaction state
  const [transactionData, setTransactionData] = useState<CustomerTransaction[]>(
    []
  );
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [refreshing, setRefreshing] = useState(false);

  // Pagination state for transactions
  const [transactionPage, setTransactionPage] = useState(1);
  const [loadingMoreTransactions, setLoadingMoreTransactions] = useState(false);
  const [hasMoreTransactions, setHasMoreTransactions] = useState(true);
  const PAGE_SIZE = 10; // Smaller page size for better UX

  // Rewards state
  const [rewardsData, setRewardsData] = useState<CustomerRewardEligibility[]>(
    []
  );
  const [loadingRewards, setLoadingRewards] = useState(false);
  const [rewardsError, setRewardsError] = useState<string | null>(null);

  const fetchTransactions = useCallback(
    async (page: number, isRefresh = false) => {
      if (!user?.id || !businessId) return;

      if (page === 1) {
        if (!isRefresh) setLoading(true);
        setError(null);
      } else {
        setLoadingMoreTransactions(true);
      }

      try {
        const result =
          await TransactionsService.getCustomerTransactionHistoryForBusiness(
            user.id,
            businessId,
            page,
            PAGE_SIZE
          );

        if (result.error) {
          setError(result.error);
          if (page === 1 && !isRefresh) setLoading(false);
          if (page > 1) setLoadingMoreTransactions(false);
          return;
        }

        const newTransactions = result.data || [];

        if (page === 1) {
          // First page - replace all data
          setTransactionData(newTransactions);
        } else {
          // Subsequent pages - append data
          setTransactionData(prev => [...prev, ...newTransactions]);
        }

        // Check if we have more data
        setHasMoreTransactions(newTransactions.length === PAGE_SIZE);

        if (page === 1 && !isRefresh) setLoading(false);
        if (page > 1) setLoadingMoreTransactions(false);
      } catch (err) {
        setError('An unexpected error occurred while fetching transactions');
        console.error('Error fetching transactions:', err);
        if (page === 1 && !isRefresh) setLoading(false);
        if (page > 1) setLoadingMoreTransactions(false);
      }
    },
    [user?.id, businessId]
  );

  const fetchRewards = useCallback(
    async (isRefresh = false) => {
      if (!user?.id || !businessId) return;

      if (!isRefresh) setLoadingRewards(true);
      setRewardsError(null);

      try {
        const result =
          await TransactionsService.getBusinessRewardsWithEligibility(
            user.id,
            businessId
          );

        if (result.error) {
          setRewardsError(result.error);
        } else {
          setRewardsData(result.data || []);
        }
      } catch (err) {
        setRewardsError('An unexpected error occurred while fetching rewards');
        console.error('Error fetching rewards:', err);
      } finally {
        if (!isRefresh) setLoadingRewards(false);
      }
    },
    [user?.id, businessId]
  );

  const fetchData = useCallback(
    async (isRefresh = false) => {
      if (!user?.id || !businessId) {
        if (!isRefresh) setLoading(false);
        return;
      }

      try {
        // Reset pagination state
        if (isRefresh) {
          setTransactionPage(1);
          setHasMoreTransactions(true);
        }

        // Fetch both transactions and rewards
        await Promise.all([
          fetchTransactions(1, isRefresh),
          fetchRewards(isRefresh),
        ]);
      } catch (err) {
        setError('An unexpected error occurred while fetching data');
        console.error('Error fetching business history data:', err);
        if (!isRefresh) setLoading(false);
      }
    },
    [user?.id, businessId, fetchTransactions, fetchRewards]
  );

  useEffect(() => {
    fetchData();
  }, [fetchData]);

  const onRefresh = async () => {
    setRefreshing(true);
    await Promise.all([
      fetchData(true),
      new Promise(resolve => setTimeout(resolve, 750)),
    ]);
    setRefreshing(false);
  };

  const loadMoreTransactions = async () => {
    if (
      loadingMoreTransactions ||
      !hasMoreTransactions ||
      !user?.id ||
      !businessId
    )
      return;

    const nextPage = transactionPage + 1;
    setTransactionPage(nextPage);
    await fetchTransactions(nextPage);
  };

  const handleBackPress = () => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    router.back();
  };

  const handleTabChange = (tab: 'transactions' | 'rewards') => {
    Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    setActiveTab(tab);
  };

  const renderTransactionItem = (item: CustomerTransaction) => (
    <Box
      key={item.id}
      className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-4'
    >
      <HStack space='md' className='items-center'>
        {/* Icon */}
        <Box
          className={`w-12 h-12 rounded-xl items-center justify-center ${
            item.type === 'purchase'
              ? 'bg-primary-500'
              : item.type === 'redemption'
                ? 'bg-error-500'
                : 'bg-secondary-500'
          }`}
        >
          <FontAwesome
            name={
              item.type === 'purchase'
                ? 'shopping-bag'
                : item.type === 'redemption'
                  ? 'gift'
                  : 'eye'
            }
            size={20}
            color='white'
          />
        </Box>

        {/* Content */}
        <VStack className='flex-1'>
          <Text size='md' className='text-typography-900 font-semibold'>
            {item.type === 'purchase'
              ? 'Purchase'
              : item.type === 'redemption'
                ? 'Redemption'
                : 'Visit'}
          </Text>
          <Text size='sm' className='text-typography-600'>
            {item.businessCategory}
          </Text>
          <Text size='xs' className='text-typography-500'>
            {dayjs(item.date).format('DD MMM YYYY, HH:mm')}
          </Text>
        </VStack>

        {/* Points */}
        <Text
          size='lg'
          className={`font-bold ${
            item.type === 'redemption' ? 'text-error-500' : 'text-primary-500'
          }`}
        >
          {item.type === 'redemption'
            ? `-${item.pointsRedeemed ?? 0} pts`
            : `${item.pointsEarned > 0 ? '+' : ''}${item.pointsEarned} pts`}
        </Text>
      </HStack>
    </Box>
  );

  const renderRewardItem = (item: CustomerRewardEligibility) => (
    <Box
      key={item.reward.id}
      className={`rounded-2xl border-4 shadow-lg p-4 ${
        item.isEligible
          ? 'bg-primary-50 border-primary-500'
          : 'bg-background-50 border-background-300'
      }`}
    >
      <VStack space='sm'>
        <HStack space='md' className='items-start justify-between'>
          <VStack space='xs' className='flex-1'>
            <Text
              size='md'
              className={`font-semibold ${
                item.isEligible ? 'text-primary-900' : 'text-typography-900'
              }`}
            >
              {item.reward.title}
            </Text>
            <Text
              size='sm'
              className={
                item.isEligible ? 'text-primary-700' : 'text-typography-600'
              }
            >
              {item.reward.description}
            </Text>
          </VStack>
          <Box
            className={`px-3 py-1 rounded-full ${
              item.isEligible ? 'bg-primary-500' : 'bg-background-300'
            }`}
          >
            <Text
              size='xs'
              className={`font-semibold ${
                item.isEligible ? 'text-white' : 'text-typography-600'
              }`}
            >
              {item.isEligible ? 'Available' : 'Locked'}
            </Text>
          </Box>
        </HStack>

        <HStack space='md' className='items-center justify-between'>
          <HStack space='sm' className='items-center'>
            <FontAwesome
              name='star'
              size={16}
              color={item.isEligible ? '#3B82F6' : '#9CA3AF'}
            />
            <Text
              size='sm'
              className={`font-medium ${
                item.isEligible ? 'text-primary-700' : 'text-typography-600'
              }`}
            >
              {item.reward.pointsRequired} points required
            </Text>
          </HStack>

          {!item.isEligible && (
            <Text size='xs' className='text-typography-500'>
              Need {item.pointsNeeded} more points
            </Text>
          )}
        </HStack>

        <HStack space='sm' className='items-center'>
          <FontAwesome name='user' size={14} color='#6B7280' />
          <Text size='xs' className='text-typography-500'>
            You have {item.customerPoints} points with this business
          </Text>
        </HStack>
      </VStack>
    </Box>
  );

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView
        className='flex-1'
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl refreshing={refreshing} onRefresh={onRefresh} />
        }
        onScroll={({ nativeEvent }) => {
          const { layoutMeasurement, contentOffset, contentSize } = nativeEvent;
          const paddingToBottom = 20;

          if (
            layoutMeasurement.height + contentOffset.y >=
            contentSize.height - paddingToBottom
          ) {
            // User has scrolled to the bottom
            loadMoreTransactions();
          }
        }}
        scrollEventThrottle={400}
      >
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Back Button and Title */}
          <HStack space='md' className='items-center'>
            <Pressable
              className='w-10 h-10 bg-primary-500 rounded-xl items-center justify-center'
              onPress={handleBackPress}
            >
              <FontAwesome name='arrow-left' size={16} color='white' />
            </Pressable>
            <VStack className='flex-1'>
              <Heading size='3xl' className='text-typography-900 font-bold'>
                {businessName || 'Business History'}
              </Heading>
              {businessCategory && (
                <Text size='md' className='text-typography-600'>
                  {businessCategory}
                </Text>
              )}
            </VStack>
          </HStack>

          {/* Colored divider line */}
          <GradientBar />
        </VStack>

        {/* Tab Navigation */}
        <Box className='px-6 pb-4'>
          <HStack space='sm' className='bg-background-100 rounded-xl p-1'>
            <Pressable
              className={`flex-1 py-3 px-4 rounded-lg items-center ${
                activeTab === 'transactions'
                  ? 'bg-white shadow-sm'
                  : 'bg-transparent'
              }`}
              onPress={() => handleTabChange('transactions')}
            >
              <Text
                size='md'
                className={`font-semibold ${
                  activeTab === 'transactions'
                    ? 'text-primary-600'
                    : 'text-typography-600'
                }`}
              >
                Transactions
              </Text>
            </Pressable>
            <Pressable
              className={`flex-1 py-3 px-4 rounded-lg items-center ${
                activeTab === 'rewards'
                  ? 'bg-white shadow-sm'
                  : 'bg-transparent'
              }`}
              onPress={() => handleTabChange('rewards')}
            >
              <Text
                size='md'
                className={`font-semibold ${
                  activeTab === 'rewards'
                    ? 'text-primary-600'
                    : 'text-typography-600'
                }`}
              >
                Rewards
              </Text>
            </Pressable>
          </HStack>
        </Box>

        <Box className='px-6 pb-8'>
          {activeTab === 'transactions' ? (
            // Transactions Tab Content
            loading && !refreshing ? (
              <Box className='flex-1 items-center justify-center py-12'>
                <ActivityIndicator size='large' color='#3B82F6' />
                <Text size='md' className='text-typography-600 mt-4'>
                  Loading transaction history...
                </Text>
              </Box>
            ) : error ? (
              <Box className='bg-error-50 border-2 border-error-500 rounded-2xl p-4'>
                <HStack space='md' className='items-center'>
                  <FontAwesome
                    name='exclamation-triangle'
                    size={20}
                    color='#EF4444'
                  />
                  <VStack className='flex-1'>
                    <Text size='md' className='text-error-700 font-semibold'>
                      Error loading data
                    </Text>
                    <Text size='sm' className='text-error-600'>
                      {error}
                    </Text>
                  </VStack>
                </HStack>
              </Box>
            ) : (
              <VStack space='md'>
                {transactionData.length === 0 ? (
                  <Box className='bg-background-50 border-2 border-background-300 rounded-2xl p-6 items-center'>
                    <FontAwesome name='history' size={40} color='#9CA3AF' />
                    <Text
                      size='md'
                      className='text-typography-600 mt-4 text-center'
                    >
                      No transactions found
                    </Text>
                    <Text
                      size='sm'
                      className='text-typography-500 mt-2 text-center'
                    >
                      Your transaction history with this business will appear
                      here
                    </Text>
                  </Box>
                ) : (
                  <>
                    {transactionData.map(renderTransactionItem)}

                    {/* Loading more indicator */}
                    {loadingMoreTransactions && (
                      <Box className='py-4 items-center'>
                        <ActivityIndicator size='small' color='#3B82F6' />
                        <Text size='sm' className='text-typography-600 mt-2'>
                          Loading more transactions...
                        </Text>
                      </Box>
                    )}

                    {/* End of list indicator */}
                    {!hasMoreTransactions && transactionData.length > 0 && (
                      <Box className='py-4 items-center'>
                        <Text size='sm' className='text-typography-500'>
                          You&apos;ve reached the end of your transaction
                          history
                        </Text>
                      </Box>
                    )}
                  </>
                )}
              </VStack>
            )
          ) : // Rewards Tab Content
          loadingRewards ? (
            <Box className='flex-1 items-center justify-center py-12'>
              <ActivityIndicator size='large' color='#3B82F6' />
              <Text size='md' className='text-typography-600 mt-4'>
                Loading rewards...
              </Text>
            </Box>
          ) : rewardsError ? (
            <Box className='bg-error-50 border-2 border-error-500 rounded-2xl p-4'>
              <HStack space='md' className='items-center'>
                <FontAwesome
                  name='exclamation-triangle'
                  size={20}
                  color='#EF4444'
                />
                <VStack className='flex-1'>
                  <Text size='md' className='text-error-700 font-semibold'>
                    Error loading rewards
                  </Text>
                  <Text size='sm' className='text-error-600'>
                    {rewardsError}
                  </Text>
                </VStack>
              </HStack>
            </Box>
          ) : (
            <VStack space='md'>
              {rewardsData.length === 0 ? (
                <Box className='bg-background-50 border-2 border-background-300 rounded-2xl p-6 items-center'>
                  <FontAwesome name='gift' size={40} color='#9CA3AF' />
                  <Text
                    size='md'
                    className='text-typography-600 mt-4 text-center'
                  >
                    No rewards available
                  </Text>
                  <Text
                    size='sm'
                    className='text-typography-500 mt-2 text-center'
                  >
                    This business hasn&apos;t configured any rewards yet
                  </Text>
                </Box>
              ) : (
                <>{rewardsData.map(renderRewardItem)}</>
              )}
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
