import { supabase } from '@indie-points/lib';
import {
  CustomerTransaction,
  CustomerBusinessSummary,
  VisitTransactionResponse,
  BusinessReward,
  CustomerRewardEligibility,
  ServiceResponse,
} from './types';

/**
 * Transactions service for handling customer transaction and business history operations
 */
export class TransactionsService {
  /**
   * Get customer transaction history including purchases and redemptions
   * @param customerId - The customer's UUID
   * @param page - Page number (default: 1)
   * @param pageSize - Number of transactions per page (default: 100)
   * @returns Promise with transaction history data or error
   */
  static async getCustomerTransactionHistory(
    customerId: string,
    page: number = 1,
    pageSize: number = 100
  ): Promise<ServiceResponse<CustomerTransaction[]>> {
    try {
      const { data, error } = await supabase.rpc(
        'get_customer_transaction_history',
        {
          p_customer_id: customerId,
          p_page: page,
          p_page_size: pageSize,
        }
      );

      if (error) {
        console.error('Error fetching customer transaction history:', error);
        return {
          data: null,
          error: error.message || 'Failed to fetch transaction history',
        };
      }

      if (!data || !Array.isArray(data)) {
        return {
          data: [],
          error: null,
        };
      }

      // Map the database response to our expected format
      const transactions: CustomerTransaction[] = data.map((item: any) => ({
        id: item.transaction_id,
        businessId: item.business_id,
        type: item.transaction_type.toLowerCase(),
        businessName: item.business_name,
        businessCategory: item.business_type,
        pointsEarned: item.points_awarded,
        pointsRedeemed: item.points_redeemed,
        amount: item.amount_spent,
        date: item.created_at,
      }));

      return {
        data: transactions,
        error: null,
      };
    } catch (error) {
      console.error(
        'Unexpected error in getCustomerTransactionHistory:',
        error
      );
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }

  /**
   * Get customer business summaries including points earned per business
   * @param customerId - The customer's UUID
   * @returns Promise with business summaries data or error
   */
  static async getCustomerBusinessSummaries(
    customerId: string
  ): Promise<ServiceResponse<CustomerBusinessSummary[]>> {
    try {
      const { data, error } = await supabase.rpc(
        'get_customer_business_summaries',
        {
          p_customer_id: customerId,
        }
      );

      if (error) {
        console.error('Error fetching customer business summaries:', error);
        return {
          data: null,
          error: error.message || 'Failed to fetch business summaries',
        };
      }

      if (!data || !Array.isArray(data)) {
        return {
          data: [],
          error: null,
        };
      }

      // Map the database response to our expected format
      const businesses: CustomerBusinessSummary[] = data.map((item: any) => ({
        id: item.business_id,
        name: item.business_name,
        category: item.business_type,
        points: item.active_points,
        lastVisit: item.last_transaction_date,
      }));

      return {
        data: businesses,
        error: null,
      };
    } catch (error) {
      console.error('Unexpected error in getCustomerBusinessSummaries:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }

  /**
   * Get customer transaction history for a specific business
   * @param customerId - The customer's UUID
   * @param businessId - The business's UUID
   * @param page - Page number (default: 1)
   * @param pageSize - Number of transactions per page (default: 100)
   * @returns Promise with transaction history data for the specific business or error
   */
  static async getCustomerTransactionHistoryForBusiness(
    customerId: string,
    businessId: string,
    page: number = 1,
    pageSize: number = 100
  ): Promise<ServiceResponse<CustomerTransaction[]>> {
    try {
      const { data, error } = await supabase.rpc(
        'get_customer_transaction_history_for_business',
        {
          p_customer_id: customerId,
          p_business_id: businessId,
          p_page: page,
          p_page_size: pageSize,
        }
      );

      if (error) {
        console.error(
          'Error fetching customer transaction history for business:',
          error
        );
        return {
          data: null,
          error:
            error.message || 'Failed to fetch transaction history for business',
        };
      }

      if (!data || !Array.isArray(data)) {
        return {
          data: [],
          error: null,
        };
      }

      // Map the database response to our expected format
      const transactions: CustomerTransaction[] = data.map((item: any) => ({
        id: item.transaction_id,
        businessId: item.business_id,
        type: item.transaction_type.toLowerCase(),
        businessName: item.business_name,
        businessCategory: item.business_type,
        pointsEarned: item.points_awarded,
        pointsRedeemed: item.points_redeemed,
        amount: item.amount_spent,
        date: item.created_at,
      }));

      return {
        data: transactions,
        error: null,
      };
    } catch (error) {
      console.error(
        'Unexpected error in getCustomerTransactionHistoryForBusiness:',
        error
      );
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }

  /**
   * Create a visit transaction for a customer at a business
   * @param customerId - The customer's UUID
   * @param businessId - The business's UUID
   * @param qrToken - The QR token from the scanned code
   * @param businessName - The business name from the scanned code
   * @returns Promise with visit transaction data or error
   */
  static async createVisitTransaction(
    customerId: string,
    businessId: string,
    businessName: string,
    qrToken: string
  ): Promise<ServiceResponse<VisitTransactionResponse>> {
    try {
      const { data, error } = await supabase.rpc(
        'create_visit_transaction_idempotent',
        {
          p_customer_id: customerId,
          p_business_id: businessId,
          p_business_name: businessName,
          p_qr_token: qrToken,
        }
      );

      if (error) {
        console.error('Error creating visit transaction:', error);
        return {
          data: null,
          error: error.message || 'Failed to create visit transaction',
        };
      }

      // The function returns an array with a single object
      const transaction = data?.[0];

      if (!transaction) {
        return {
          data: null,
          error: 'No transaction data returned',
        };
      }

      return {
        data: {
          alreadyExists: transaction.already_exists,
        },
        error: null,
      };
    } catch (error) {
      console.error('Unexpected error in createVisitTransaction:', error);
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }

  /**
   * Get available rewards for a business with customer eligibility
   * @param customerId - The customer's UUID
   * @param businessId - The business's UUID
   * @returns Promise with rewards and eligibility data or error
   */
  static async getBusinessRewardsWithEligibility(
    customerId: string,
    businessId: string
  ): Promise<ServiceResponse<CustomerRewardEligibility[]>> {
    try {
      // First get the business rewards
      const { data: rewardsData, error: rewardsError } = await supabase
        .from('business_rewards')
        .select('*')
        .eq('business_id', businessId)
        .eq('is_active', true)
        .order('points_required', { ascending: true });

      if (rewardsError) {
        console.error('Error fetching business rewards:', rewardsError);
        return {
          data: null,
          error: rewardsError.message || 'Failed to fetch business rewards',
        };
      }

      if (!rewardsData || rewardsData.length === 0) {
        return {
          data: [],
          error: null,
        };
      }

      // Get customer's current points for this business
      const { data: pointsData, error: pointsError } = await supabase.rpc(
        'get_customer_business_summaries',
        {
          p_customer_id: customerId,
        }
      );

      if (pointsError) {
        console.error('Error fetching customer points:', pointsError);
        return {
          data: null,
          error: pointsError.message || 'Failed to fetch customer points',
        };
      }

      // Find the customer's points for this specific business
      const businessSummary = pointsData?.find(
        (summary: any) => summary.business_id === businessId
      );
      const customerPoints = businessSummary?.points || 0;

      // Map rewards with eligibility information
      const rewardsWithEligibility: CustomerRewardEligibility[] =
        rewardsData.map((reward: any) => {
          const isEligible = customerPoints >= reward.points_required;
          const pointsNeeded = Math.max(
            0,
            reward.points_required - customerPoints
          );

          return {
            reward: {
              id: reward.id,
              businessId: reward.business_id,
              title: reward.title,
              description: reward.description,
              pointsRequired: reward.points_required,
              isActive: reward.is_active,
              createdAt: reward.created_at,
              updatedAt: reward.updated_at,
            },
            isEligible,
            customerPoints,
            pointsNeeded,
          };
        });

      return {
        data: rewardsWithEligibility,
        error: null,
      };
    } catch (error) {
      console.error(
        'Unexpected error in getBusinessRewardsWithEligibility:',
        error
      );
      return {
        data: null,
        error:
          error instanceof Error
            ? error.message
            : 'An unexpected error occurred',
      };
    }
  }
}
