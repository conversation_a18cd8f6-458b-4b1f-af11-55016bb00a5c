import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>iew } from 'react-native';
import { router } from 'expo-router';
import { Box } from '@indie-points/ui-box';
import { VStack } from '@indie-points/ui-vstack';
import { HStack } from '@indie-points/ui-hstack';
import { Heading } from '@indie-points/ui-heading';
import { Text } from '@indie-points/ui-text';
import { Button, ButtonText } from '@indie-points/ui-button';
import { Input, InputField } from '@indie-points/ui-input';
import { Spinner } from '@indie-points/ui-spinner';
import {
  Slider,
  SliderThumb,
  SliderTrack,
  SliderFilledTrack,
} from '@indie-points/ui-slider';
import { useAuth } from '@indie-points/contexts';
import { BusinessService, BusinessProfile } from '../../services';
import FontAwesome from '@expo/vector-icons/FontAwesome';
import { <PERSON><PERSON><PERSON><PERSON>ar } from '@indie-points/auth';
import * as Haptics from 'expo-haptics';

export default function Settings() {
  const { user, signOut } = useAuth();
  const [businessProfile, setBusinessProfile] =
    useState<BusinessProfile | null>(null);
  const [loading, setLoading] = useState(true);
  const [saving, setSaving] = useState(false);
  const [editing, setEditing] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Form state
  const [businessName, setBusinessName] = useState('');
  const [businessType, setBusinessType] = useState('');
  const [pointValuePercentage, setPointValuePercentage] = useState('');
  const [minimumSpendForRedemption, setMinimumSpendForRedemption] =
    useState('');

  // Fetch business profile
  useEffect(() => {
    const fetchBusinessProfile = async () => {
      if (!user?.id) {
        setLoading(false);
        return;
      }

      const result = await BusinessService.getBusinessProfile(user.id);

      if (result.error) {
        setError(result.error);
        setBusinessProfile(null);
        // Set default values for new profile
        setBusinessName('');
        setBusinessType('');
        setPointValuePercentage('5.0');
        setMinimumSpendForRedemption('20');
      } else if (!result.data) {
        // No profile exists, set defaults for creation
        setBusinessProfile(null);
        setBusinessName('');
        setBusinessType('');
        setPointValuePercentage('5.0');
        setMinimumSpendForRedemption('20');
        setEditing(true); // Start in editing mode if no profile exists
      } else {
        // Profile exists, populate form
        setBusinessProfile(result.data);
        setBusinessName(result.data.businessName);
        setBusinessType(result.data.businessType);
        setPointValuePercentage(result.data.pointValuePercentage.toString());
        setMinimumSpendForRedemption(
          result.data.minimumSpendForRedemption.toString()
        );
      }

      setLoading(false);
    };

    fetchBusinessProfile();
  }, [user?.id]);

  const handleSaveProfile = async () => {
    if (!user?.id) return;

    const name = businessName.trim();
    const type = businessType.trim();
    const pointValue = parseFloat(pointValuePercentage);
    const minSpend = parseFloat(minimumSpendForRedemption);

    if (!name || !type) {
      setError('Please fill in all required fields.');
      return;
    }

    if (isNaN(pointValue) || pointValue < 2.5 || pointValue > 10) {
      setError('Point value percentage must be between 2.5% and 10%.');
      return;
    }

    if (isNaN(minSpend) || minSpend < 5 || minSpend > 50) {
      setError('Minimum spend must be between £5 and £50.');
      return;
    }

    setSaving(true);
    setError(null);

    try {
      const result = await BusinessService.upsertBusinessProfile(user.id, {
        businessName: name,
        businessType: type,
        pointValuePercentage: pointValue,
        minimumSpendForRedemption: minSpend,
        onboardingCompleted: true,
        onboardingStep: 4,
      });

      if (result.error) {
        setError(result.error);
      } else if (result.data) {
        setBusinessProfile(result.data);
        setEditing(false);
        Haptics.notificationAsync(Haptics.NotificationFeedbackType.Success);
      }
    } catch (error) {
      console.error('Error saving business profile:', error);
      setError('Failed to save business profile. Please try again.');
    } finally {
      setSaving(false);
    }
  };

  const handleSignOut = async () => {
    await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
    Alert.alert('Sign out', 'Are you sure you want to sign out?', [
      {
        text: 'Cancel',
        style: 'cancel',
      },
      {
        text: 'Sign out',
        style: 'destructive',
        onPress: async () => {
          await Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Medium);
          const { error } = await signOut();
          if (!error) {
            router.replace('/(auth)/sign-in');
          }
        },
      },
    ]);
  };

  return (
    <Box className='flex-1 bg-background-0'>
      <ScrollView className='flex-1' showsVerticalScrollIndicator={false}>
        {/* Header Section */}
        <VStack space='lg' className='px-6 pt-6 pb-6'>
          {/* Title */}
          <Heading size='3xl' className='text-typography-900 font-bold'>
            Settings
          </Heading>

          {/* Colored divider line */}
          <GradientBar />
        </VStack>

        <Box className='flex-1 px-6 pb-8'>
          {loading ? (
            // Loading state
            <VStack space='lg' className='items-center py-8'>
              <Spinner size='large' />
              <Text size='md' className='text-typography-600'>
                Loading business profile...
              </Text>
            </VStack>
          ) : (
            <VStack space='xl' className='flex-1'>
              {/* User Profile Card */}
              <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
                <HStack space='md' className='items-center'>
                  {/* Profile Icon */}
                  <Box className='w-16 h-16 bg-primary-500 rounded-2xl items-center justify-center'>
                    <FontAwesome name='user' size={24} color='white' />
                  </Box>

                  {/* User Info */}
                  <VStack className='flex-1'>
                    <Text
                      size='lg'
                      className='text-typography-900 font-semibold'
                    >
                      {user?.email || 'Not signed in'}
                    </Text>
                    <Text size='sm' className='text-typography-600'>
                      Business owner
                    </Text>
                  </VStack>
                </HStack>
              </Box>

              {/* Error Message */}
              {error && (
                <Box className='bg-error-50 border-2 border-error-500 rounded-2xl p-4'>
                  <HStack space='md' className='items-center'>
                    <FontAwesome
                      name='exclamation-triangle'
                      size={20}
                      color='#EF4444'
                    />
                    <Text
                      size='md'
                      className='text-error-700 font-semibold flex-1'
                    >
                      {error}
                    </Text>
                  </HStack>
                </Box>
              )}

              {/* Business Profile Card */}
              <Box className='bg-white rounded-2xl border-4 border-typography-900 shadow-lg p-6'>
                <VStack space='lg'>
                  <HStack space='md' className='items-center justify-between'>
                    <HStack space='md' className='items-center'>
                      <FontAwesome name='building' size={24} color='#1f2937' />
                      <Heading
                        size='lg'
                        className='text-typography-900 font-semibold'
                      >
                        Business profile
                      </Heading>
                    </HStack>
                    {businessProfile && !editing && (
                      <Button
                        size='sm'
                        action='secondary'
                        onPress={() => {
                          Haptics.impactAsync(
                            Haptics.ImpactFeedbackStyle.Medium
                          );
                          setEditing(true);
                        }}
                      >
                        <ButtonText>Edit</ButtonText>
                      </Button>
                    )}
                  </HStack>

                  {editing ? (
                    // Edit mode
                    <VStack space='md'>
                      <VStack space='sm'>
                        <Text
                          size='md'
                          className='text-typography-900 font-medium'
                        >
                          Business name *
                        </Text>
                        <Input>
                          <InputField
                            placeholder='Enter your business name'
                            value={businessName}
                            onChangeText={setBusinessName}
                          />
                        </Input>
                      </VStack>

                      <VStack space='sm'>
                        <Text
                          size='md'
                          className='text-typography-900 font-medium'
                        >
                          Business type *
                        </Text>
                        <Input>
                          <InputField
                            placeholder='e.g., Restaurant, Retail, Gym'
                            value={businessType}
                            onChangeText={setBusinessType}
                          />
                        </Input>
                      </VStack>

                      <VStack space='sm'>
                        <Text
                          size='md'
                          className='text-typography-900 font-medium'
                        >
                          Point value percentage *
                        </Text>
                        <HStack space='md' className='items-center'>
                          <Slider
                            value={parseFloat(pointValuePercentage)}
                            onChange={(value: number) => {
                              setPointValuePercentage(value.toString());
                              Haptics.impactAsync(
                                Haptics.ImpactFeedbackStyle.Light
                              );
                            }}
                            minValue={2.5}
                            maxValue={10}
                            step={0.5}
                            className='flex-1 h-12'
                          >
                            <SliderTrack className='w-full h-3 bg-gray-200 rounded-full'>
                              <SliderFilledTrack className='h-full bg-primary-500 rounded-full' />
                            </SliderTrack>
                            <SliderThumb className='w-10 h-10 bg-primary-500 rounded-full shadow-lg border-2 border-white' />
                          </Slider>
                          <Text
                            size='md'
                            className='text-typography-900 font-semibold min-w-[40px] text-center'
                          >
                            {pointValuePercentage}%
                          </Text>
                        </HStack>
                      </VStack>

                      <VStack space='sm'>
                        <Text
                          size='md'
                          className='text-typography-900 font-medium'
                        >
                          Minimum spend for redemption *
                        </Text>
                        <HStack space='md' className='items-center'>
                          <Slider
                            value={parseFloat(minimumSpendForRedemption)}
                            onChange={(value: number) => {
                              setMinimumSpendForRedemption(value.toString());
                              Haptics.impactAsync(
                                Haptics.ImpactFeedbackStyle.Light
                              );
                            }}
                            minValue={5}
                            maxValue={50}
                            step={1}
                            className='flex-1 h-12'
                          >
                            <SliderTrack className='w-full h-3 bg-gray-200 rounded-full'>
                              <SliderFilledTrack className='h-full bg-primary-500 rounded-full' />
                            </SliderTrack>
                            <SliderThumb className='w-10 h-10 bg-primary-500 rounded-full shadow-lg border-2 border-white' />
                          </Slider>
                          <Text
                            size='md'
                            className='text-typography-900 font-semibold min-w-[50px] text-center'
                          >
                            £{minimumSpendForRedemption}
                          </Text>
                        </HStack>
                      </VStack>

                      <HStack space='md'>
                        <Button
                          size='lg'
                          action='secondary'
                          className='flex-1'
                          onPress={() => {
                            setEditing(false);
                            setError(null);
                            // Reset form to original values
                            if (businessProfile) {
                              setBusinessName(businessProfile.businessName);
                              setBusinessType(businessProfile.businessType);
                              setPointValuePercentage(
                                businessProfile.pointValuePercentage.toString()
                              );
                              setMinimumSpendForRedemption(
                                businessProfile.minimumSpendForRedemption.toString()
                              );
                            }
                          }}
                        >
                          <ButtonText>Cancel</ButtonText>
                        </Button>
                        <Button
                          size='lg'
                          action='primary'
                          className='flex-1'
                          onPress={handleSaveProfile}
                          disabled={saving}
                        >
                          {saving ? (
                            <Spinner size='small' color='white' />
                          ) : (
                            <ButtonText>Save</ButtonText>
                          )}
                        </Button>
                      </HStack>
                    </VStack>
                  ) : businessProfile ? (
                    // View mode
                    <VStack space='md'>
                      <VStack space='sm'>
                        <Text
                          size='sm'
                          className='text-typography-600 font-medium'
                        >
                          Business name
                        </Text>
                        <Text size='md' className='text-typography-900'>
                          {businessProfile.businessName}
                        </Text>
                      </VStack>

                      <VStack space='sm'>
                        <Text
                          size='sm'
                          className='text-typography-600 font-medium'
                        >
                          Business type
                        </Text>
                        <Text size='md' className='text-typography-900'>
                          {businessProfile.businessType}
                        </Text>
                      </VStack>

                      <HStack space='md'>
                        <VStack space='sm' className='flex-1'>
                          <Text
                            size='sm'
                            className='text-typography-600 font-medium'
                          >
                            Point value
                          </Text>
                          <Text size='md' className='text-typography-900'>
                            {businessProfile.pointValuePercentage}% of spend
                          </Text>
                        </VStack>

                        <VStack space='sm' className='flex-1'>
                          <Text
                            size='sm'
                            className='text-typography-600 font-medium'
                          >
                            Min. redemption
                          </Text>
                          <Text size='md' className='text-typography-900'>
                            £{businessProfile.minimumSpendForRedemption}
                          </Text>
                        </VStack>
                      </HStack>
                    </VStack>
                  ) : (
                    // No profile exists
                    <VStack space='md' className='items-center'>
                      <FontAwesome
                        name='building-o'
                        size={48}
                        color='#9CA3AF'
                      />
                      <Text
                        size='md'
                        className='text-typography-600 text-center'
                      >
                        No business profile found
                      </Text>
                      <Text
                        size='sm'
                        className='text-typography-500 text-center'
                      >
                        Create your business profile to start using the app
                      </Text>
                    </VStack>
                  )}
                </VStack>
              </Box>

              {/* Sign Out Button */}
              <Button
                size='lg'
                onPress={handleSignOut}
                className='w-full bg-error-500 rounded-xl border-2 border-error-700 shadow-lg'
              >
                <ButtonText className='text-white font-semibold text-lg'>
                  Sign out
                </ButtonText>
              </Button>
            </VStack>
          )}
        </Box>
      </ScrollView>
    </Box>
  );
}
