// Types for service layer responses

// Business-specific types
export interface BusinessProfile {
  id: string;
  userId: string;
  businessName: string;
  businessAddress?: string;
  businessType: string;
  pointValuePercentage: number;
  minimumSpendForRedemption: number;
  onboardingCompleted: boolean;
  onboardingStep: number;
  createdAt: string;
  updatedAt: string;
}

export interface BusinessPointsSummary {
  totalEarned: number;
  totalActive: number;
  totalRedeemed: number;
}

export interface BusinessCustomerSummary {
  customerId: string;
  customerDisplayName: string;
  activePoints: number;
  earnedPoints: number;
  redeemedPoints: number;
  lastTransactionDate: string;
  totalTransactions: number;
  totalAmountSpent: number;
}

export interface BusinessTransaction {
  id: string;
  customerId: string;
  businessId: string;
  amountSpent: number;
  pointsAwarded: number;
  pointsRedeemed: number;
  qrToken: string;
  type: 'purchase' | 'redemption' | 'visit';
  createdAt: string;
  updatedAt: string;
}

export interface PurchaseTransactionRequest {
  customerId: string;
  businessId: string;
  amountSpent: number;
  qrToken: string;
}

export interface ServiceResponse<T> {
  data: T | null;
  error: string | null;
}
